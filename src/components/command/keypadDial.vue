<template>
  <KeepAlive>
    <component
      :is="showNetworkSpeaking ? bfSpeaking : DialPad"
      v-bind="dialogProps"
      v-model="dialogVisible"
      ref="dynamicRef"
      @switchToKeypadDialing="handleSwitchToKeypadDialing"
      @switchToNetworkSpeaking="handleSwitchToNetworkSpeaking"
    />
  </KeepAlive>
</template>

<script setup lang="ts">
  import { ref, computed } from 'vue'
  import bfSpeaking from './bfSpeaking.vue'
  import DialPad from './DialPad.vue'

  // 定义props
  const props = defineProps<{
    dialogVisible?: boolean
  }>()

  // 定义emits
  const emit = defineEmits<{
    'update:dialogVisible': [value: boolean]
  }>()

  // 响应式数据
  const dialogVisible = computed({
    get: () => props.dialogVisible || false,
    set: value => emit('update:dialogVisible', value),
  })

  const showNetworkSpeaking = ref(false)
  const dynamicRef = ref()

  // 传递给子组件的props
  const dialogProps = computed(() => ({
    // 这里可以添加需要传递给子组件的其他props
  }))

  // 处理从联网通话切换回键盘拨号
  const handleSwitchToKeypadDialing = () => {
    showNetworkSpeaking.value = false
  }

  // 处理从拨号盘切换到联网通话
  const handleSwitchToNetworkSpeaking = () => {
    showNetworkSpeaking.value = true
  }
</script>
